import { spawn } from "child_process";
import { promises as fs } from "fs";
import { join, dirname } from "path";
import { tmpdir } from "os";
import { EventEmitter } from "events";
import type { FileInfo } from "./fileSelector";

/**
 * 打包任务状态
 */
export type PackingStatus = "pending" | "packing" | "completed" | "failed" | "cancelled";

/**
 * 打包任务接口
 */
export interface PackingTask {
  id: string;
  files: FileInfo[];
  outputPath: string;
  progress: number;
  status: PackingStatus;
  startTime: Date;
  endTime?: Date;
  error?: string;
  totalSize: number;
  processedSize: number;
}

/**
 * 打包选项
 */
export interface PackingOptions {
  compressionLevel?: number; // 压缩级别 0-9，默认1（最快）
  outputDir?: string; // 输出目录，默认临时目录
  outputName?: string; // 输出文件名，默认自动生成
  password?: string; // 压缩包密码
  includeEmptyDirs?: boolean; // 是否包含空目录
}

/**
 * 智能打包器
 * 使用7z进行文件压缩，支持进度回调和错误处理
 */
export class SmartPacker extends EventEmitter {
  private static instance: SmartPacker;
  private tasks = new Map<string, PackingTask>();
  private activeProcesses = new Map<string, any>();

  // 打包阈值：文件数量超过此值时自动打包
  public static readonly PACKING_THRESHOLD = 50;

  public static getInstance(): SmartPacker {
    if (!SmartPacker.instance) {
      SmartPacker.instance = new SmartPacker();
    }
    return SmartPacker.instance;
  }

  /**
   * 判断是否需要打包
   */
  static shouldPack(files: FileInfo[]): boolean {
    return files.length > SmartPacker.PACKING_THRESHOLD;
  }

  /**
   * 创建打包任务
   */
  async createPackingTask(files: FileInfo[], options: PackingOptions = {}): Promise<string> {
    const taskId = this.generateTaskId();
    const outputDir = options.outputDir || tmpdir();
    const outputName = options.outputName || `upload_${Date.now()}.7z`;
    const outputPath = join(outputDir, outputName);

    // 确保输出目录存在
    await fs.mkdir(dirname(outputPath), { recursive: true });

    const task: PackingTask = {
      id: taskId,
      files,
      outputPath,
      progress: 0,
      status: "pending",
      startTime: new Date(),
      totalSize: files.reduce((sum, file) => sum + file.size, 0),
      processedSize: 0,
    };

    this.tasks.set(taskId, task);
    this.emit("task-created", taskId, task);

    return taskId;
  }

  /**
   * 开始打包
   */
  async startPacking(taskId: string, options: PackingOptions = {}): Promise<void> {
    const task = this.tasks.get(taskId);
    if (!task) {
      throw new Error(`打包任务不存在: ${taskId}`);
    }

    if (task.status !== "pending") {
      throw new Error(`任务状态不正确: ${task.status}`);
    }

    try {
      this.updateTaskStatus(taskId, "packing");
      await this.executePackingProcess(task, options);
    } catch (error) {
      this.handlePackingError(taskId, error);
      throw error;
    }
  }

  /**
   * 取消打包任务
   */
  async cancelPacking(taskId: string): Promise<void> {
    const task = this.tasks.get(taskId);
    if (!task) {
      throw new Error(`打包任务不存在: ${taskId}`);
    }

    const process = this.activeProcesses.get(taskId);
    if (process) {
      process.kill("SIGTERM");
      this.activeProcesses.delete(taskId);
    }

    this.updateTaskStatus(taskId, "cancelled");
    this.emit("task-cancelled", taskId);
  }

  /**
   * 获取任务信息
   */
  getTask(taskId: string): PackingTask | undefined {
    return this.tasks.get(taskId);
  }

  /**
   * 获取所有任务
   */
  getAllTasks(): PackingTask[] {
    return Array.from(this.tasks.values());
  }

  /**
   * 清理已完成的任务
   */
  async clearCompletedTasks(): Promise<void> {
    const completedTasks = Array.from(this.tasks.entries()).filter(([_, task]) => task.status === "completed" || task.status === "failed");

    for (const [taskId, task] of completedTasks) {
      // 清理临时文件
      if (task.status === "failed" || task.status === "completed") {
        try {
          await fs.unlink(task.outputPath);
        } catch (error) {
          // 忽略文件删除错误
        }
      }
      this.tasks.delete(taskId);
    }
  }

  /**
   * 执行打包过程
   */
  private async executePackingProcess(task: PackingTask, options: PackingOptions): Promise<void> {
    const compressionLevel = options.compressionLevel ?? 1; // 默认最快压缩

    // 构建7z命令参数
    const args = [
      "a", // 添加文件到压缩包
      `-mx=${compressionLevel}`, // 压缩级别
      task.outputPath, // 输出文件路径
    ];

    // 添加密码参数
    if (options.password) {
      args.push(`-p${options.password}`);
    }

    // 添加文件路径
    task.files.forEach((file) => {
      args.push(file.path);
    });

    return new Promise((resolve, reject) => {
      try {
        // 获取7z可执行文件路径
        const sevenZipPath = this.get7zPath();

        const process = spawn(sevenZipPath, args, {
          stdio: ["pipe", "pipe", "pipe"],
        });

        this.activeProcesses.set(task.id, process);

        let stdout = "";
        let stderr = "";

        // 处理标准输出
        process.stdout?.on("data", (data) => {
          stdout += data.toString();
          this.parseProgress(task.id, stdout);
        });

        // 处理错误输出
        process.stderr?.on("data", (data) => {
          stderr += data.toString();
        });

        // 处理进程结束
        process.on("close", (code) => {
          this.activeProcesses.delete(task.id);

          if (code === 0) {
            this.handlePackingSuccess(task.id);
            resolve();
          } else {
            const error = new Error(`7z进程退出，代码: ${code}, 错误: ${stderr}`);
            this.handlePackingError(task.id, error);
            reject(error);
          }
        });

        // 处理进程错误
        process.on("error", (error) => {
          this.activeProcesses.delete(task.id);
          this.handlePackingError(task.id, error);
          reject(error);
        });
      } catch (error) {
        this.handlePackingError(task.id, error);
        reject(error);
      }
    });
  }

  /**
   * 解析7z输出中的进度信息
   */
  private parseProgress(taskId: string, output: string): void {
    const task = this.tasks.get(taskId);
    if (!task) return;

    // 7z输出格式可能有多种：
    // "  0%  1 - filename.txt"
    // "Compressing  filename.txt    0%"
    // "Everything is Ok"

    // 尝试匹配百分比
    const progressMatches = output.match(/(\d+)%/g);
    if (progressMatches && progressMatches.length > 0) {
      // 取最后一个百分比值（通常是最新的进度）
      const lastProgressStr = progressMatches[progressMatches.length - 1];
      const progress = parseInt(lastProgressStr.replace("%", ""), 10);

      if (progress >= 0 && progress <= 100 && progress > task.progress) {
        task.progress = progress;
        task.processedSize = Math.floor((progress / 100) * task.totalSize);

        this.emit("task-progress", taskId, progress, task.processedSize, task.totalSize);
      }
    }

    // 检查是否完成
    if (output.includes("Everything is Ok") || output.includes("Files read from disk:")) {
      task.progress = 100;
      task.processedSize = task.totalSize;
      this.emit("task-progress", taskId, 100, task.totalSize, task.totalSize);
    }
  }

  /**
   * 处理打包成功
   */
  private handlePackingSuccess(taskId: string): void {
    const task = this.tasks.get(taskId);
    if (!task) return;

    task.status = "completed";
    task.progress = 100;
    task.processedSize = task.totalSize;
    task.endTime = new Date();

    this.emit("task-completed", taskId, task);
  }

  /**
   * 处理打包错误
   */
  private handlePackingError(taskId: string, error: any): void {
    const task = this.tasks.get(taskId);
    if (!task) return;

    task.status = "failed";
    task.error = error.message || String(error);
    task.endTime = new Date();

    this.emit("task-failed", taskId, error);
  }

  /**
   * 更新任务状态
   */
  private updateTaskStatus(taskId: string, status: PackingStatus): void {
    const task = this.tasks.get(taskId);
    if (task) {
      task.status = status;
      this.emit("task-status-changed", taskId, status);
    }
  }

  /**
   * 生成任务ID
   */
  private generateTaskId(): string {
    return `pack_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取7z可执行文件路径
   */
  private get7zPath(): string {
    try {
      // 尝试使用7zip-bin包
      const sevenBin = require("7zip-bin");
      return sevenBin.path7za;
    } catch (error) {
      // 降级到系统7z命令
      return "7z";
    }
  }
}
