import { ipcMain } from "electron";
import type { TusUploadManager } from "./uploadManager";
import type { TusUploadConfig, ApiResponse } from "./types";
import { ElectronFileSelector, type FileSelectOptions, type FileSelectResult } from "./fileSelector";
import { SmartPacker, type PackingOptions, type PackingTask } from "./smartPacker";
import { UploadStrategyAnalyzer } from "./uploadStrategyAnalyzer";
// import { DragDropHandler } from "./dragDropHandler"; // 已在 dragDropHandler.registerIpcHandlers() 中使用
import type { UploadStrategyAnalysis } from "./newUploadTypes";

/**
 * 注册所有 TUS 相关的 IPC 处理器
 */
export function registerTusIpcHandlers(uploadManager: TusUploadManager) {
  const fileSelector = ElectronFileSelector.getInstance();
  const smartPacker = SmartPacker.getInstance();
  const strategyAnalyzer = UploadStrategyAnalyzer.getInstance();
  // const dragDropHandler = DragDropHandler.getInstance(); // 已在 dragDropHandler.registerIpcHandlers() 中使用
  // 创建上传任务
  ipcMain.handle("tus-create-upload", async (_event, filePath: string, metadata?: Record<string, string>): Promise<ApiResponse> => {
    try {
      const taskId = await uploadManager.createUploadTask(filePath, metadata);
      return { success: true, taskId };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 开始上传
  ipcMain.handle("tus-start-upload", async (_event, taskId: string): Promise<ApiResponse> => {
    try {
      await uploadManager.startUpload(taskId);
      return { success: true };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 暂停上传
  ipcMain.handle("tus-pause-upload", async (_event, taskId: string): Promise<ApiResponse> => {
    try {
      await uploadManager.pauseUpload(taskId);
      return { success: true };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 恢复上传
  ipcMain.handle("tus-resume-upload", async (_event, taskId: string): Promise<ApiResponse> => {
    try {
      await uploadManager.resumeUpload(taskId);
      return { success: true };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 取消上传
  ipcMain.handle("tus-cancel-upload", async (_event, taskId: string): Promise<ApiResponse> => {
    try {
      await uploadManager.cancelUpload(taskId);
      return { success: true };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 重试上传
  ipcMain.handle("tus-retry-upload", async (_event, taskId: string): Promise<ApiResponse> => {
    try {
      await uploadManager.retryUpload(taskId);
      return { success: true };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 删除任务
  ipcMain.handle("tus-delete-task", async (_event, taskId: string): Promise<ApiResponse> => {
    try {
      await uploadManager.deleteTask(taskId);
      return { success: true };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 获取所有任务
  ipcMain.handle("tus-get-all-tasks", async (_event): Promise<ApiResponse> => {
    try {
      const tasks = uploadManager.getAllTasks();
      return { success: true, tasks };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 获取指定任务
  ipcMain.handle("tus-get-task", async (_event, taskId: string): Promise<ApiResponse> => {
    try {
      const task = uploadManager.getTask(taskId);
      return { success: true, task };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 获取活跃任务
  ipcMain.handle("tus-get-active-tasks", async (_event): Promise<ApiResponse> => {
    try {
      const tasks = uploadManager.getActiveTasks();
      return { success: true, tasks };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 获取任务的上传URL
  ipcMain.handle("tus-get-task-upload-url", async (_event, taskId: string): Promise<ApiResponse> => {
    try {
      const uploadUrl = uploadManager.getTaskUploadUrl(taskId);
      return { success: true, data: { uploadUrl } };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 更新配置
  ipcMain.handle("tus-update-config", async (_event, config: Partial<TusUploadConfig>): Promise<ApiResponse> => {
    try {
      uploadManager.updateConfig(config);
      return { success: true };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 清理已完成的任务
  ipcMain.handle("tus-clear-completed-tasks", async (_event): Promise<ApiResponse> => {
    try {
      uploadManager.clearCompletedTasks();
      return { success: true };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 清空所有任务
  ipcMain.handle("tus-clear-all-tasks", async (_event): Promise<ApiResponse> => {
    try {
      await uploadManager.clearAllTasks();
      return { success: true };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 从 File 对象创建上传任务（将文件内容保存为临时文件）
  ipcMain.handle(
    "tus-create-upload-from-file",
    async (
      _event,
      fileData: {
        name: string;
        content: ArrayBuffer;
        metadata?: Record<string, string>;
      }
    ): Promise<ApiResponse> => {
      try {
        const path = await import("path");
        const fs = await import("fs/promises");
        const os = await import("os");

        // 处理文件名，替换路径分隔符并确保文件名安全
        const safeName = fileData.name.replace(/[/\\:*?"<>|]/g, "_");

        // 创建临时文件
        const tempDir = os.tmpdir();
        const tempFileName = `upload_${Date.now()}_${safeName}`;
        const tempFilePath = path.join(tempDir, tempFileName);

        // 写入文件内容
        await fs.writeFile(tempFilePath, Buffer.from(fileData.content));

        // 创建上传任务，保持原始文件名信息在元数据中
        const enhancedMetadata = {
          ...fileData.metadata,
          originalName: fileData.name, // 保留原始文件名（可能包含路径）
          tempFilePath: tempFilePath, // 记录临时文件路径
        };

        const taskId = await uploadManager.createUploadTask(tempFilePath, enhancedMetadata);

        return { success: true, taskId };
      } catch (error) {
        return { success: false, error: String(error) };
      }
    }
  );

  // 通过文件选择对话框创建上传任务（避免通过 IPC 传递大文件内容）
  ipcMain.handle("tus-create-upload-from-dialog", async (_event): Promise<ApiResponse> => {
    try {
      const { dialog } = await import("electron");

      const result = await dialog.showOpenDialog({
        properties: ["openFile", "multiSelections"],
        title: "选择要上传的文件",
        filters: [
          { name: "所有文件", extensions: ["*"] },
          { name: "图片", extensions: ["jpg", "jpeg", "png", "gif", "bmp", "webp"] },
          { name: "视频", extensions: ["mp4", "avi", "mov", "wmv", "flv", "webm"] },
          { name: "文档", extensions: ["pdf", "doc", "docx", "txt", "rtf"] },
        ],
      });

      if (result.canceled || result.filePaths.length === 0) {
        return { success: false, error: "用户取消了文件选择" };
      }

      // 创建多个上传任务
      const taskIds: string[] = [];

      for (const filePath of result.filePaths) {
        const taskId = await uploadManager.createUploadTask(filePath);
        taskIds.push(taskId);
      }

      return {
        success: true,
        data: { taskIds, count: taskIds.length },
      };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 从文件路径列表创建批量上传任务（推荐用于大文件）
  ipcMain.handle("tus-create-uploads-from-paths", async (_event, filePaths: string[], metadata?: Record<string, string>): Promise<ApiResponse> => {
    try {
      const taskIds: string[] = [];

      for (const filePath of filePaths) {
        const taskId = await uploadManager.createUploadTask(filePath, metadata);
        taskIds.push(taskId);
      }

      return {
        success: true,
        data: { taskIds, count: taskIds.length },
      };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 获取文件信息（不读取文件内容，仅获取基本信息）
  ipcMain.handle("tus-get-file-info", async (_event, filePath: string): Promise<ApiResponse> => {
    try {
      const fs = await import("fs/promises");
      const path = await import("path");

      const stats = await fs.stat(filePath);
      const fileName = path.basename(filePath);

      return {
        success: true,
        data: {
          path: filePath,
          name: fileName,
          size: stats.size,
          isFile: stats.isFile(),
          isDirectory: stats.isDirectory(),
          lastModified: stats.mtime.getTime(),
        },
      };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // ==================== 新增：文件选择相关处理器 ====================

  // 选择文件
  ipcMain.handle("file-select-files", async (_event, options?: FileSelectOptions): Promise<ApiResponse<FileSelectResult>> => {
    try {
      const result = await fileSelector.selectFiles(options);
      return { success: result.success, data: result, error: result.error };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 选择文件夹
  ipcMain.handle("file-select-directories", async (_event, options?: FileSelectOptions): Promise<ApiResponse<FileSelectResult>> => {
    try {
      const result = await fileSelector.selectDirectories(options);
      return { success: result.success, data: result, error: result.error };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 处理文件路径列表
  ipcMain.handle("file-process-paths", async (_event, filePaths: string[]): Promise<ApiResponse<FileSelectResult>> => {
    try {
      const files = await fileSelector.processFilePaths(filePaths);
      const result: FileSelectResult = {
        success: true,
        files,
        totalSize: files.reduce((sum, file) => sum + file.size, 0),
        totalCount: files.length,
      };
      return { success: true, data: result };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // ==================== 新增：拖拽处理相关处理器 ====================
  // 注意：drag-drop-process-files 处理器已在 dragDropHandler.registerIpcHandlers() 中注册
  // 这里不需要重复注册，避免 "Attempted to register a second handler" 错误

  // ==================== 新增：上传策略分析相关处理器 ====================

  // 分析上传策略
  ipcMain.handle("upload-analyze-strategy", async (_event, files: any[]): Promise<ApiResponse<UploadStrategyAnalysis>> => {
    try {
      const analysis = strategyAnalyzer.analyzeUploadStrategy(files);
      return { success: true, data: analysis };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 获取策略建议
  ipcMain.handle("upload-get-strategy-recommendation", async (_event, analysis: UploadStrategyAnalysis): Promise<ApiResponse<string>> => {
    try {
      const recommendation = strategyAnalyzer.getStrategyRecommendation(analysis);
      return { success: true, data: recommendation };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 比较上传策略
  ipcMain.handle("upload-compare-strategies", async (_event, files: any[], strategy1: string, strategy2: string): Promise<ApiResponse<any>> => {
    try {
      const comparison = strategyAnalyzer.compareStrategies(files, strategy1 as any, strategy2 as any);
      return { success: true, data: comparison };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // ==================== 新增：智能打包相关处理器 ====================

  // 判断是否需要打包
  ipcMain.handle("pack-should-pack", async (_event, fileCount: number): Promise<ApiResponse<boolean>> => {
    try {
      const shouldPack = fileCount > SmartPacker.PACKING_THRESHOLD;
      return { success: true, data: shouldPack };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 创建打包任务
  ipcMain.handle("pack-create-task", async (_event, files: any[], options?: PackingOptions): Promise<ApiResponse<string>> => {
    try {
      const taskId = await smartPacker.createPackingTask(files, options);
      return { success: true, data: taskId };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 开始打包
  ipcMain.handle("pack-start", async (_event, taskId: string, options?: PackingOptions): Promise<ApiResponse> => {
    try {
      await smartPacker.startPacking(taskId, options);
      return { success: true };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 取消打包
  ipcMain.handle("pack-cancel", async (_event, taskId: string): Promise<ApiResponse> => {
    try {
      await smartPacker.cancelPacking(taskId);
      return { success: true };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 获取打包任务
  ipcMain.handle("pack-get-task", async (_event, taskId: string): Promise<ApiResponse<PackingTask | null>> => {
    try {
      const task = smartPacker.getTask(taskId);
      return { success: true, data: task || null };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 获取所有打包任务
  ipcMain.handle("pack-get-all-tasks", async (_event): Promise<ApiResponse<PackingTask[]>> => {
    try {
      const tasks = smartPacker.getAllTasks();
      return { success: true, data: tasks };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 清理已完成的打包任务
  ipcMain.handle("pack-clear-completed", async (_event): Promise<ApiResponse> => {
    try {
      await smartPacker.clearCompletedTasks();
      return { success: true };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // ==================== 智能上传处理器 ====================

  // 智能上传：自动判断是否需要打包
  ipcMain.handle("upload-smart", async (_event, files: any[], metadata?: Record<string, string>): Promise<ApiResponse<{ taskIds: string[]; packingTaskId?: string }>> => {
    try {
      const shouldPack = SmartPacker.shouldPack(files);

      if (shouldPack) {
        // 需要打包：先打包再上传
        const packingTaskId = await smartPacker.createPackingTask(files);

        // 启动打包任务（异步）
        smartPacker.startPacking(packingTaskId).catch((error) => {
          console.error(`打包任务 ${packingTaskId} 失败:`, error);
        });

        // 返回打包任务ID，让前端监听打包完成事件
        return {
          success: true,
          data: {
            taskIds: [], // 打包完成后才会有上传任务ID
            packingTaskId,
          },
        };
      } else {
        // 不需要打包：直接上传
        const taskIds: string[] = [];
        for (const file of files) {
          const taskId = await uploadManager.createUploadTask(file.path, metadata);
          taskIds.push(taskId);
        }

        return {
          success: true,
          data: { taskIds },
        };
      }
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 打包完成后创建上传任务
  ipcMain.handle("upload-after-packing", async (_event, packingTaskId: string, metadata?: Record<string, string>): Promise<ApiResponse<{ taskIds: string[] }>> => {
    try {
      const packingTask = smartPacker.getTask(packingTaskId);
      if (!packingTask) {
        throw new Error(`打包任务不存在: ${packingTaskId}`);
      }

      if (packingTask.status !== "completed") {
        throw new Error(`打包任务未完成，当前状态: ${packingTask.status}`);
      }

      // 创建压缩包的上传任务
      const uploadTaskId = await uploadManager.createUploadTask(packingTask.outputPath, {
        ...metadata,
        originalFileCount: packingTask.files.length.toString(),
        isPacked: "true",
        packingTaskId,
        originalName: `${packingTask.files.length}个文件的压缩包.7z`,
      });

      return {
        success: true,
        data: { taskIds: [uploadTaskId] },
      };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });
}

/**
 * 移除所有 TUS 相关的 IPC 处理器
 */
export function unregisterTusIpcHandlers() {
  const handlers = [
    // 原有的TUS处理器
    "tus-create-upload",
    "tus-create-upload-from-file",
    "tus-start-upload",
    "tus-pause-upload",
    "tus-resume-upload",
    "tus-cancel-upload",
    "tus-retry-upload",
    "tus-delete-task",
    "tus-get-all-tasks",
    "tus-get-task",
    "tus-get-active-tasks",
    "tus-update-config",
    "tus-clear-completed-tasks",
    "tus-clear-all-tasks",
    "tus-create-upload-from-dialog",
    "tus-create-uploads-from-paths",
    "tus-get-file-info",

    // 新增的文件选择处理器
    "file-select-files",
    "file-select-directories",
    "file-process-paths",

    // 新增的拖拽处理器
    "drag-drop-process-files",

    // 新增的策略分析处理器
    "upload-analyze-strategy",
    "upload-get-strategy-recommendation",
    "upload-compare-strategies",

    // 新增的打包处理器
    "pack-should-pack",
    "pack-create-task",
    "pack-start",
    "pack-cancel",
    "pack-get-task",
    "pack-get-all-tasks",
    "pack-clear-completed",

    // 新增的智能上传处理器
    "upload-smart",
    "upload-after-packing",
  ];

  handlers.forEach((handler) => {
    ipcMain.removeHandler(handler);
  });
}
