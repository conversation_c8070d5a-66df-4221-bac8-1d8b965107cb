/**
 * Electron相关类型定义
 */

// ==================== 文件信息类型 ====================

/**
 * Electron文件信息
 */
export interface FileInfo {
  path: string; // 文件绝对路径
  name: string; // 文件名
  size: number; // 文件大小
  type: string; // MIME类型
  relativePath?: string; // 相对路径（用于文件夹上传）
  isDirectory: boolean; // 是否为目录
  lastModified: number; // 最后修改时间
}

/**
 * 文件选择选项
 */
export interface FileSelectOptions {
  title?: string;
  buttonLabel?: string;
  filters?: Electron.FileFilter[];
  properties?: Array<"openFile" | "openDirectory" | "multiSelections" | "showHiddenFiles">;
  defaultPath?: string;
}

/**
 * 文件选择结果
 */
export interface FileSelectResult {
  success: boolean;
  files: FileInfo[];
  totalSize: number;
  totalCount: number;
  error?: string;
}

// ==================== 拖拽相关类型 ====================

/**
 * 拖拽文件信息
 */
export interface DragDropFileInfo {
  path: string;
  name: string;
  size: number;
  type: string;
  isDirectory: boolean;
}

/**
 * 拖拽事件数据
 */
export interface DragDropEventData {
  files: DragDropFileInfo[];
  totalSize: number;
  totalCount: number;
  hasDirectories: boolean;
}

// ==================== 打包相关类型 ====================

/**
 * 打包任务状态
 */
export type PackingStatus = "pending" | "packing" | "completed" | "failed" | "cancelled";

/**
 * 打包任务
 */
export interface PackingTask {
  id: string;
  files: FileInfo[];
  outputPath: string;
  progress: number;
  status: PackingStatus;
  startTime: Date;
  endTime?: Date;
  error?: string;
  totalSize: number;
  processedSize: number;
}

/**
 * 打包选项
 */
export interface PackingOptions {
  compressionLevel?: number; // 压缩级别 0-9，默认1（最快）
  outputDir?: string; // 输出目录，默认临时目录
  outputName?: string; // 输出文件名，默认自动生成
  password?: string; // 压缩包密码
  includeEmptyDirs?: boolean; // 是否包含空目录
}

// ==================== 上传策略相关类型 ====================

/**
 * 上传策略类型
 */
export type UploadStrategy = "individual" | "batch" | "smart-pack";

/**
 * 上传策略分析结果
 */
export interface UploadStrategyAnalysis {
  strategy: UploadStrategy;
  shouldPack: boolean;
  fileCount: number;
  totalSize: number;
  estimatedTime: number;
  reason: string;
}

// ==================== 上传任务相关类型 ====================

/**
 * 上传任务状态
 */
export type UploadStatus = "pending" | "uploading" | "paused" | "completed" | "error" | "cancelled";

/**
 * 上传任务
 */
export interface UploadTask {
  id: string;
  fileName: string;
  filePath: string;
  fileSize: number;
  progress: number;
  status: UploadStatus;
  uploadUrl?: string;
  bytesUploaded: number;
  uploadSpeed: number;
  remainingTime: number;
  error?: string;
  createdAt: Date;
  updatedAt: Date;
  startTime?: Date;
  endTime?: Date;
  metadata?: Record<string, string>;
  // 批量任务相关属性
  isSubTask?: boolean;
  batchId?: string;
}

/**
 * 批量上传任务
 */
export interface BatchUploadTask {
  id: string;
  name: string;
  batchName?: string;
  subTasks: string[];
  progress: number;
  status: UploadStatus;
  totalFiles: number;
  completedFiles: number;
  totalSize: number;
  uploadedSize: number;
  createdAt: Date;
  updatedAt: Date;
  startTime?: Date;
  endTime?: Date;
  failedFiles?: number;
  folderPath?: string;
  expanded?: boolean;
  type?: string;
}

/**
 * TUS上传配置
 */
export interface TusUploadConfig {
  endpoint: string;
  chunkSize: number;
  retryDelays: number[];
  parallelUploads: number;
  headers?: Record<string, string>;
}

// ==================== API响应类型 ====================

/**
 * API响应基础接口
 */
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  taskId?: string;
  task?: UploadTask;
  tasks?: UploadTask[];
}

// ==================== Electron API接口 ====================

/**
 * TUS上传API接口
 */
export interface TusElectronAPI {
  // 文件选择
  selectFiles: (options?: FileSelectOptions) => Promise<ApiResponse<FileSelectResult>>;
  selectDirectories: (options?: FileSelectOptions) => Promise<ApiResponse<FileSelectResult>>;
  processFilePaths: (filePaths: string[]) => Promise<ApiResponse<FileSelectResult>>;

  // 拖拽处理
  processDragDropFiles: (filePaths: string[]) => Promise<ApiResponse<DragDropEventData>>;

  // 上传策略分析
  analyzeUploadStrategy: (files: FileInfo[]) => Promise<ApiResponse<UploadStrategyAnalysis>>;
  getStrategyRecommendation: (analysis: UploadStrategyAnalysis) => Promise<ApiResponse<string>>;
  compareStrategies: (files: FileInfo[], strategy1: string, strategy2: string) => Promise<ApiResponse<any>>;

  // 智能上传
  smartUpload: (files: FileInfo[], metadata?: Record<string, string>) => Promise<ApiResponse<{ taskIds: string[]; packingTaskId?: string }>>;
  uploadAfterPacking: (packingTaskId: string, metadata?: Record<string, string>) => Promise<ApiResponse<{ taskIds: string[] }>>;

  // 打包相关
  shouldPack: (fileCount: number) => Promise<ApiResponse<boolean>>;
  createPackingTask: (files: FileInfo[], options?: PackingOptions) => Promise<ApiResponse<string>>;
  startPacking: (taskId: string, options?: PackingOptions) => Promise<ApiResponse>;
  cancelPacking: (taskId: string) => Promise<ApiResponse>;
  getPackingTask: (taskId: string) => Promise<ApiResponse<PackingTask | null>>;
  getAllPackingTasks: () => Promise<ApiResponse<PackingTask[]>>;
  clearCompletedPackingTasks: () => Promise<ApiResponse>;

  // 原有的TUS方法（保持兼容性）
  createUpload: (filePath: string, metadata?: Record<string, string>) => Promise<ApiResponse>;
  createUploadFromFile: (fileData: { name: string; content: ArrayBuffer; metadata?: Record<string, string> }) => Promise<ApiResponse>;
  createUploadFromDialog: () => Promise<ApiResponse>;
  createUploadsFromPaths: (filePaths: string[], metadata?: Record<string, string>) => Promise<ApiResponse>;
  getFileInfo: (filePath: string) => Promise<ApiResponse>;
  startUpload: (taskId: string) => Promise<ApiResponse>;
  pauseUpload: (taskId: string) => Promise<ApiResponse>;
  resumeUpload: (taskId: string) => Promise<ApiResponse>;
  cancelUpload: (taskId: string) => Promise<ApiResponse>;
  retryUpload: (taskId: string) => Promise<ApiResponse>;
  deleteTask: (taskId: string) => Promise<ApiResponse>;
  getAllTasks: () => Promise<ApiResponse>;
  getTask: (taskId: string) => Promise<ApiResponse>;
  getActiveTasks: () => Promise<ApiResponse>;
  getTaskUploadUrl: (taskId: string) => Promise<ApiResponse>;
  updateConfig: (config: any) => Promise<ApiResponse>;
  clearCompletedTasks: () => Promise<ApiResponse>;
  clearAllTasks: () => Promise<ApiResponse>;
}

/**
 * 下载API接口
 */
export interface DownloadElectronAPI {
  // API 调用方法
  createTask: (fileName?: string, savePath?: string, metadata?: Record<string, string>) => Promise<any>;
  createTasks: (downloads: Array<{ fileName?: string; savePath?: string; metadata?: Record<string, string> }>) => Promise<any>;
  createTaskWithDialog: (defaultFileName?: string, metadata?: Record<string, string>) => Promise<any>;
  startDownload: (taskId: string) => Promise<any>;
  pauseDownload: (taskId: string) => Promise<any>;
  resumeDownload: (taskId: string) => Promise<any>;
  cancelDownload: (taskId: string) => Promise<any>;
  retryDownload: (taskId: string) => Promise<any>;
  deleteTask: (taskId: string) => Promise<any>;
  getAllTasks: () => Promise<any>;
  getTask: (taskId: string) => Promise<any>;
  getActiveTasks: () => Promise<any>;
  updateConfig: (config: any) => Promise<any>;
  clearCompletedTasks: () => Promise<any>;
  startBatch: (taskIds: string[]) => Promise<any>;
  pauseBatch: (taskIds: string[]) => Promise<any>;
  resumeBatch: (taskIds: string[]) => Promise<any>;
  getStats: () => Promise<any>;
  getUnfinishedTasks: () => Promise<any>;
  clearAllTasks: () => Promise<any>;
  showSelectFolderDialog: (defaultPath?: string) => Promise<any>;
  getDefaultDownloadPath: () => Promise<any>;

  // 事件监听器方法
  onDownloadTaskCreated?: (callback: (taskId: string, task: any) => void) => void;
  onDownloadTaskProgress?: (callback: (taskId: string, progress: number, bytesDownloaded: number, bytesTotal: number) => void) => void;
  onDownloadTaskStatusChanged?: (callback: (taskId: string, status: string, error?: string) => void) => void;
  onDownloadTaskCompleted?: (callback: (taskId: string) => void) => void;
  onDownloadTaskError?: (callback: (taskId: string, error: string) => void) => void;
  removeAllListeners?: (channel: string) => void;
}

/**
 * Electron API全局接口
 */
export interface ElectronAPI {
  // 基础功能
  getAppVersion: () => Promise<string>;
  getPlatform: () => Promise<string>;
  showOpenDialog: (options: any) => Promise<any>;
  showSaveDialog: (options: any) => Promise<any>;
  showMessageBox: (options: any) => Promise<any>;
  getFileInfo: (filePath: string) => Promise<any>;

  // TUS 上传功能
  tus: TusElectronAPI;

  // StreamSaver 下载功能
  download: DownloadElectronAPI;

  // 主进程消息监听器
  onMainProcessMessage: (callback: (data: string) => void) => void;

  // 认证相关 API
  onAuthToken: (callback: (token: string) => void) => void;
  clearAuthState: () => void;
}

// ==================== 工具类型 ====================

/**
 * 文件类型检测结果
 */
export interface FileTypeInfo {
  category: "image" | "video" | "audio" | "document" | "archive" | "code" | "other";
  mimeType: string;
  extension: string;
  icon: string;
}

/**
 * 文件上传进度信息
 */
export interface UploadProgress {
  taskId: string;
  fileName: string;
  progress: number;
  bytesUploaded: number;
  bytesTotal: number;
  uploadSpeed: number;
  remainingTime: number;
  status: string;
}

/**
 * 批量操作结果
 */
export interface BatchOperationResult {
  success: number;
  failed: number;
  total: number;
  errors: string[];
}

// ==================== 事件类型 ====================

/**
 * 文件选择事件
 */
export interface FileSelectEvent {
  type: "start" | "progress" | "complete" | "error";
  data?: {
    progress?: number;
    currentFile?: string;
    result?: FileSelectResult;
    error?: string;
  };
}

/**
 * 打包进度事件
 */
export interface PackingProgressEvent {
  taskId: string;
  progress: number;
  processedSize: number;
  totalSize: number;
  currentFile?: string;
  speed?: number;
  remainingTime?: number;
}

/**
 * 上传事件
 */
export interface UploadEvent {
  type: "created" | "progress" | "completed" | "error" | "cancelled";
  taskId: string;
  data?: any;
}

export default {};
