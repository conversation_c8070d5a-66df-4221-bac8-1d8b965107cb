<template>
  <div class="upload-test-page">
    <div class="header">
      <h1>上传功能测试页面</h1>
      <p>用于测试和调试文件上传功能</p>
    </div>

    <div class="test-sections">
      <!-- 调试信息区域 -->
      <div class="debug-section">
        <h2>调试信息</h2>
        <div class="debug-buttons">
          <button @click="debugTusInfo" class="debug-btn">TUS任务信息</button>
          <button @click="debugProgressInfo" class="debug-btn">进度信息</button>
          <button @click="refreshTasks" class="debug-btn">刷新任务</button>
          <button @click="forceSync" class="debug-btn">强制同步</button>
        </div>

        <div class="debug-stats">
          <div class="stat-item">
            <span class="label">TUS任务数:</span>
            <span class="value">{{ tusUpload.tasks.size }}</span>
          </div>
          <div class="stat-item">
            <span class="label">独立任务数:</span>
            <span class="value">{{ tusUpload.standaloneTasks.length }}</span>
          </div>
          <div class="stat-item">
            <span class="label">进度任务数:</span>
            <span class="value">{{ globalProgress.tasks.length }}</span>
          </div>
          <div class="stat-item">
            <span class="label">活跃任务数:</span>
            <span class="value">{{ globalProgress.activeTasks.length }}</span>
          </div>
        </div>
      </div>

      <!-- 上传测试区域 -->
      <div class="upload-section">
        <h2>上传测试</h2>
        <div class="upload-buttons">
          <button @click="testFileUpload" class="upload-btn">测试文件上传</button>
          <button @click="testFolderUpload" class="upload-btn">测试文件夹上传</button>
        </div>
      </div>

      <!-- 任务列表区域 -->
      <div class="tasks-section">
        <h2>当前任务列表</h2>
        <div class="tasks-list">
          <div v-if="globalProgress.tasks.length === 0" class="no-tasks">
            暂无任务
          </div>
          <div v-else>
            <div v-for="task in globalProgress.tasks" :key="task.id" class="task-item" :class="{
              'task-uploading': task.status === 'in-progress',
              'task-completed': task.status === 'completed',
              'task-error': task.status === 'error'
            }">
              <div class="task-info">
                <div class="task-name">{{ task.fileName }}</div>
                <div class="task-details">
                  <span class="task-type">{{ task.type }}</span>
                  <span class="task-status">{{ task.status }}</span>
                  <span class="task-progress">{{ task.progress }}%</span>
                  <span class="task-size">{{ task.size }}</span>
                </div>
              </div>
              <div class="task-progress-bar">
                <div class="progress-fill" :style="{ width: task.progress + '%' }"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- TUS任务详情 -->
      <div class="tus-section">
        <h2>TUS任务详情</h2>
        <div class="tus-tasks">
          <div v-if="tusUpload.standaloneTasks.length === 0" class="no-tasks">
            暂无TUS任务
          </div>
          <div v-else>
            <div v-for="task in tusUpload.standaloneTasks" :key="task.id" class="tus-task-item">
              <div class="tus-task-info">
                <div class="tus-task-name">{{ task.fileName }}</div>
                <div class="tus-task-details">
                  <span>ID: {{ task.id }}</span>
                  <span>状态: {{ task.status }}</span>
                  <span>进度: {{ task.progress }}%</span>
                  <span>大小: {{ formatFileSize(task.fileSize) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// import { ref } from 'vue' // 暂时未使用
import { useElectronTusUpload } from '@/components/Upload/composables/useElectronTusUpload'
import { useGlobalProgress } from '@/composables/useGlobalProgress'
import { formatFileSize } from '@/lib/upload-utils'
import { toast } from 'vue-sonner'

const tusUpload = useElectronTusUpload()
const globalProgress = useGlobalProgress()

// 调试方法
const debugTusInfo = () => {
  tusUpload.debugTasksInfo()
}

const debugProgressInfo = () => {
  globalProgress.debugProgressInfo()
}

const refreshTasks = async () => {
  console.log('[测试] 开始刷新任务...')
  await tusUpload.refreshTasks()
  console.log('[测试] 任务刷新完成')
  toast.success('任务列表已刷新')
}

const forceSync = () => {
  console.log('[测试] 开始强制同步...')
  globalProgress.forceSync()
  toast.success('强制同步完成')
}

// 上传测试方法
const testFileUpload = async () => {
  try {
    console.log('[测试] 开始文件上传测试...')
    await tusUpload.uploadFromDialog()
    toast.success('文件上传测试启动成功')
  } catch (error) {
    console.error('[测试] 文件上传测试失败:', error)
    toast.error('文件上传测试失败')
  }
}

const testFolderUpload = async () => {
  try {
    console.log('[测试] 开始文件夹上传测试...')
    // 这里可以添加文件夹上传的测试逻辑
    toast.info('文件夹上传测试功能待实现')
  } catch (error) {
    console.error('[测试] 文件夹上传测试失败:', error)
    toast.error('文件夹上传测试失败')
  }
}
</script>

<style scoped>
.upload-test-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.header h1 {
  color: #333;
  margin-bottom: 10px;
}

.header p {
  color: #666;
}

.test-sections {
  display: grid;
  gap: 20px;
}

.debug-section,
.upload-section,
.tasks-section,
.tus-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e9ecef;
}

.debug-section h2,
.upload-section h2,
.tasks-section h2,
.tus-section h2 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #495057;
}

.debug-buttons,
.upload-buttons {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.debug-btn,
.upload-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.debug-btn {
  background: #007bff;
  color: white;
}

.debug-btn:hover {
  background: #0056b3;
}

.upload-btn {
  background: #28a745;
  color: white;
}

.upload-btn:hover {
  background: #1e7e34;
}

.debug-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 12px;
  background: white;
  border-radius: 4px;
  border: 1px solid #dee2e6;
}

.label {
  font-weight: 500;
  color: #495057;
}

.value {
  font-weight: bold;
  color: #007bff;
}

.tasks-list,
.tus-tasks {
  max-height: 400px;
  overflow-y: auto;
}

.no-tasks {
  text-align: center;
  color: #6c757d;
  padding: 20px;
  font-style: italic;
}

.task-item,
.tus-task-item {
  background: white;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 8px;
  border: 1px solid #dee2e6;
}

.task-item.task-uploading {
  border-left: 4px solid #007bff;
}

.task-item.task-completed {
  border-left: 4px solid #28a745;
}

.task-item.task-error {
  border-left: 4px solid #dc3545;
}

.task-name,
.tus-task-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.task-details,
.tus-task-details {
  display: flex;
  gap: 15px;
  font-size: 12px;
  color: #6c757d;
}

.task-progress-bar {
  height: 4px;
  background: #e9ecef;
  border-radius: 2px;
  margin-top: 8px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #007bff;
  transition: width 0.3s ease;
}
</style>
