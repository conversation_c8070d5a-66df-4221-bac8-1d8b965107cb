import { ref, computed, onMounted, onUnmounted } from "vue";
import { toast } from "vue-sonner";
import type { ElectronUploadFile } from "./useElectronFileUpload";
import type { FileInfo, UploadStrategyAnalysis, ApiResponse, UploadTask, BatchUploadTask } from "@/types/electron";
import { debug } from "@/lib/debug";
// import { generateUniqueId } from "@/lib/utils"; // 暂时未使用
import { formatFileSize } from "@/lib/upload-utils";

// 上传任务状态类型
export type UploadStatus = "pending" | "uploading" | "paused" | "completed" | "error" | "cancelled";

// 日志记录器
const tusLogger = {
  event: (message: string, ...args: any[]) => console.log(`[TUS事件] ${message}`, ...args),
  task: (message: string, ...args: any[]) => console.log(`[TUS任务] ${message}`, ...args),
  batch: (message: string, ...args: any[]) => console.log(`[TUS批量] ${message}`, ...args),
  error: (message: string, ...args: any[]) => console.error(`[TUS错误] ${message}`, ...args),
  warn: (message: string, ...args: any[]) => console.warn(`[TUS警告] ${message}`, ...args),
};

/**
 * Electron TUS上传管理
 */
export function useElectronTusUpload() {
  // 响应式状态
  const isUploading = ref(false);
  const uploadProgress = ref<Record<string, number>>({});
  const uploadTasks = ref<Record<string, any>>({});
  const packingTasks = ref<Record<string, any>>({});
  const currentStrategy = ref<UploadStrategyAnalysis | null>(null);

  // 任务管理状态
  const tasks = ref<Map<string, UploadTask>>(new Map());
  const batchTasks = ref<Map<string, BatchUploadTask>>(new Map());
  const isListenersSetup = ref(false);

  // 检查是否在 Electron 环境中
  const isElectron = computed(() => {
    return typeof window !== "undefined" && !!(window as any).electronAPI;
  });

  // 获取Electron API
  const getElectronAPI = () => {
    const api = (window as any).electronAPI;
    if (!api) {
      throw new Error("Electron API 不可用");
    }
    return api;
  };

  // 获取TUS API
  const getTusAPI = () => {
    const api = getElectronAPI();
    if (!api.tus) {
      throw new Error("Electron TUS API 不可用");
    }
    return api.tus;
  };

  // 事件处理器
  const handleTaskCreated = (taskId: string, task: UploadTask) => {
    debug.task.create(taskId, task.fileName);

    if (tasks.value.has(taskId)) {
      tusLogger.task(`任务已存在，跳过: ${task.fileName}`);
      return;
    }

    tusLogger.event(`✅ 任务创建事件 - ${task.fileName} (${taskId})`);
    console.log(`[TUS] 任务创建: ${task.fileName}, ID: ${taskId}, 状态: ${task.status}`);

    // 从 metadata 中读取子任务标识并设置到任务对象上
    const enhancedTask = { ...task };
    if (task.metadata?.isSubTask === "true") {
      enhancedTask.isSubTask = true;
      enhancedTask.batchId = task.metadata.batchId;
      tusLogger.batch(`子任务标识已设置: ${task.fileName} -> batchId: ${task.metadata.batchId}`);
    }

    tasks.value.set(taskId, enhancedTask);
    console.log(`[TUS] 任务已添加到本地存储, 当前任务总数: ${tasks.value.size}`);

    if (enhancedTask.batchId) {
      updateBatchTaskProgress(enhancedTask.batchId);
    }
  };

  const handleTaskProgress = (taskId: string, progress: number, bytesUploaded: number, _bytesTotal: number) => {
    const task = tasks.value.get(taskId);
    if (task) {
      const updatedTask = {
        ...task,
        progress: Math.round(progress),
        bytesUploaded,
        status: task.status === "pending" ? ("uploading" as const) : task.status,
      };
      tasks.value.set(taskId, updatedTask);

      if (task.batchId) {
        updateBatchTaskProgress(task.batchId);
      }
    }
  };

  const handleTaskStatusChanged = async (taskId: string, status: string, error?: string) => {
    const task = tasks.value.get(taskId);
    if (task) {
      const updatedTask = {
        ...task,
        status: status as UploadStatus,
        error,
      };

      if (["completed", "error", "cancelled"].includes(status)) {
        updatedTask.progress = status === "completed" ? 100 : task.progress;
      }

      tasks.value.set(taskId, updatedTask);
      tusLogger.task(`任务状态变化: ${task.fileName} -> ${status}${error ? ` (错误: ${error})` : ""}`);

      if (task.batchId) {
        updateBatchTaskProgress(task.batchId);
      }

      if (status === "completed") {
        debug.upload.complete(task.fileName);
      } else if (status === "error" && error) {
        debug.upload.error(task.fileName, error);
      }
    }
  };

  const handleTaskCompleted = async (taskId: string) => {
    const task = tasks.value.get(taskId);
    if (task) {
      const updatedTask = {
        ...task,
        status: "completed" as UploadStatus,
        progress: 100,
      };
      tasks.value.set(taskId, updatedTask);

      if (task.batchId) {
        updateBatchTaskProgress(task.batchId);
      }

      debug.upload.complete(task.fileName);
    }
  };

  const handleTaskError = (taskId: string, error: string) => {
    const task = tasks.value.get(taskId);
    if (task) {
      const updatedTask = { ...task, status: "error" as UploadStatus, error };
      tasks.value.set(taskId, updatedTask);
      tusLogger.error(`任务出错 - ${task.fileName}: ${error}`);

      if (task.batchId) {
        updateBatchTaskProgress(task.batchId);
      }

      debug.upload.error(task.fileName, error);
    }
  };

  // 批量任务管理
  const updateBatchTaskProgress = (batchId: string) => {
    const batch = batchTasks.value.get(batchId);
    if (!batch) return;

    const batchTaskIds = Array.from(tasks.value.values())
      .filter((task) => task.batchId === batchId)
      .map((task) => task.id);

    if (batchTaskIds.length === 0) return;

    let completedFiles = 0;
    let failedFiles = 0;
    let uploadingFiles = 0;
    let pausedFiles = 0;
    let totalProgress = 0;

    batchTaskIds.forEach((taskId) => {
      const task = tasks.value.get(taskId);
      if (task) {
        totalProgress += task.progress;
        switch (task.status) {
          case "completed":
            completedFiles++;
            break;
          case "error":
          case "cancelled":
            failedFiles++;
            break;
          case "uploading":
            uploadingFiles++;
            break;
          case "paused":
            pausedFiles++;
            break;
        }
      }
    });

    const actualTotalFiles = batchTaskIds.length;
    const averageProgress = actualTotalFiles > 0 ? Math.round(totalProgress / actualTotalFiles) : 0;

    let batchStatus: UploadStatus = "pending";
    if (completedFiles === actualTotalFiles) {
      batchStatus = "completed";
      tusLogger.batch(`✅ 批量任务完成: ${batch.batchName} (${completedFiles}/${actualTotalFiles})`);
    } else if (failedFiles > 0 && completedFiles + failedFiles === actualTotalFiles) {
      batchStatus = "error";
      tusLogger.batch(`❌ 批量任务失败: ${batch.batchName} (完成=${completedFiles}, 失败=${failedFiles}, 总计=${actualTotalFiles})`);
    } else if (uploadingFiles > 0) {
      batchStatus = "uploading";
    } else if (pausedFiles > 0) {
      batchStatus = "paused";
    }

    const updatedBatch: BatchUploadTask = {
      ...batch,
      progress: averageProgress,
      status: batchStatus,
      completedFiles,
      failedFiles,
      totalFiles: actualTotalFiles,
    };

    batchTasks.value.set(batchId, updatedBatch);
  };

  // 事件监听器设置
  const setupEventListeners = () => {
    if (!isElectron.value) {
      tusLogger.warn("TUS上传功能仅在Electron环境中可用");
      return;
    }

    if (isListenersSetup.value) {
      tusLogger.event("TUS事件监听器已设置，跳过重复设置");
      return;
    }

    try {
      const api = getTusAPI();

      api.onUploadTaskCreated(handleTaskCreated);
      api.onUploadTaskProgress(handleTaskProgress);
      api.onUploadTaskStatusChanged(handleTaskStatusChanged);
      api.onUploadTaskCompleted(handleTaskCompleted);
      api.onUploadTaskError(handleTaskError);

      isListenersSetup.value = true;
      tusLogger.event("TUS事件监听器设置完成");
    } catch (error) {
      tusLogger.error("设置 TUS 事件监听器失败:", error);
      throw error;
    }
  };

  const setupEventListenersWithRetry = async (maxRetries: number = 3, delay: number = 1000): Promise<void> => {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        setupEventListeners();

        if (isListenersSetup.value) {
          tusLogger.event(`TUS事件监听器设置成功 (尝试 ${attempt}/${maxRetries})`);
          return;
        } else {
          throw new Error("事件监听器设置状态验证失败");
        }
      } catch (error) {
        tusLogger.warn(`TUS事件监听器设置失败 (尝试 ${attempt}/${maxRetries}):`, error);

        if (attempt === maxRetries) {
          tusLogger.error("TUS事件监听器设置最终失败，已达到最大重试次数");
          throw error;
        }

        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }
  };

  const cleanupEventListeners = () => {
    if (!isElectron.value) return;

    if (!isListenersSetup.value) {
      tusLogger.event("TUS事件监听器未设置，跳过清理");
      return;
    }

    try {
      const api = getTusAPI();

      api.removeAllListeners("upload-task-created");
      api.removeAllListeners("upload-task-progress");
      api.removeAllListeners("upload-task-status-changed");
      api.removeAllListeners("upload-task-completed");
      api.removeAllListeners("upload-task-error");

      isListenersSetup.value = false;
      tusLogger.event("TUS事件监听器清理完成");
    } catch (error) {
      tusLogger.error("清理 TUS 事件监听器失败:", error);
      isListenersSetup.value = false;
    }
  };

  /**
   * 智能上传文件
   */
  const smartUploadFiles = async (files: ElectronUploadFile[], metadata?: Record<string, string>): Promise<{ success: boolean; taskIds: string[]; packingTaskId?: string }> => {
    if (!files || files.length === 0) {
      toast.error("没有选择文件");
      return { success: false, taskIds: [] };
    }

    try {
      isUploading.value = true;
      const tusAPI = getTusAPI();

      // 转换为FileInfo格式
      const fileInfos: FileInfo[] = files.map((file) => ({
        path: file.path,
        name: file.name,
        size: file.size,
        type: file.type,
        relativePath: file.relativePath,
        isDirectory: file.isDirectory,
        lastModified: file.lastModified,
      }));

      // 分析上传策略 - 使用 TUS API
      const strategyResult = await tusAPI.analyzeUploadStrategy(fileInfos);
      if (!strategyResult.success) {
        throw new Error(strategyResult.error || "分析上传策略失败");
      }

      currentStrategy.value = strategyResult.data;

      // 显示策略建议 - 使用 TUS API
      const recommendationResult = await tusAPI.getStrategyRecommendation(strategyResult.data);
      if (recommendationResult.success) {
        toast.info(recommendationResult.data);
      }

      // 执行智能上传 - 使用 TUS API
      const uploadResult = await tusAPI.smartUpload(fileInfos, metadata);
      if (!uploadResult.success) {
        throw new Error(uploadResult.error || "智能上传失败");
      }

      const { taskIds, packingTaskId } = uploadResult.data;

      // 如果需要打包，监听打包完成事件
      if (packingTaskId) {
        toast.info("文件数量较多，正在压缩打包...");
        await monitorPackingTask(packingTaskId, metadata);
      } else {
        // 直接上传，等待任务创建确认后启动
        await waitForTasksCreation(taskIds);

        // 启动所有任务
        for (const taskId of taskIds) {
          await tusAPI.startUpload(taskId);
        }
        toast.success(`已开始上传 ${taskIds.length} 个文件`);
      }

      return { success: true, taskIds, packingTaskId };
    } catch (error) {
      console.error("智能上传失败:", error);
      toast.error(`上传失败: ${error instanceof Error ? error.message : String(error)}`);
      return { success: false, taskIds: [] };
    } finally {
      isUploading.value = false;
    }
  };

  /**
   * 等待任务创建确认
   */
  const waitForTasksCreation = async (taskIds: string[], timeout: number = 10000): Promise<void> => {
    const startTime = Date.now();
    const checkInterval = 500; // 每500ms检查一次

    console.log(`等待 ${taskIds.length} 个任务创建确认...`);

    while (Date.now() - startTime < timeout) {
      try {
        // 检查所有任务是否都已创建
        const api = getTusAPI();
        const response = await api.getAllTasks();

        if (response.success && response.tasks) {
          const existingTaskIds = response.tasks.map((task) => task.id);
          const allTasksExist = taskIds.every((taskId) => existingTaskIds.includes(taskId));

          if (allTasksExist) {
            console.log(`所有 ${taskIds.length} 个任务创建确认完成`);
            return;
          }
        }

        // 等待后继续检查
        await new Promise((resolve) => setTimeout(resolve, checkInterval));
      } catch (error) {
        console.warn("检查任务创建状态时出错:", error);
        await new Promise((resolve) => setTimeout(resolve, checkInterval));
      }
    }

    // 超时处理
    console.warn(`任务创建确认超时 (${timeout}ms)，尝试刷新任务列表`);
    try {
      const api = getTusAPI();
      await api.refreshTasks();
    } catch (error) {
      console.error("刷新任务列表失败:", error);
    }
  };

  /**
   * 监听打包任务
   */
  const monitorPackingTask = async (packingTaskId: string, metadata?: Record<string, string>): Promise<void> => {
    return new Promise((resolve, reject) => {
      const api = getElectronAPI();

      // 监听打包完成事件
      const handlePackingComplete = async (event: any, taskId: string, task: any) => {
        if (taskId === packingTaskId) {
          try {
            toast.success("文件打包完成，开始上传压缩包");

            // 创建上传任务
            const uploadResult = await api.uploadAfterPacking(packingTaskId, metadata);
            if (uploadResult.success) {
              // 启动上传任务
              for (const uploadTaskId of uploadResult.data.taskIds) {
                await api.startUpload(uploadTaskId);
              }
              toast.success("压缩包上传已开始");
            } else {
              throw new Error(uploadResult.error || "创建上传任务失败");
            }

            resolve();
          } catch (error) {
            reject(error);
          } finally {
            // 清理事件监听器
            window.removeEventListener("packing-task-completed", handlePackingComplete);
            window.removeEventListener("packing-task-failed", handlePackingFailed);
          }
        }
      };

      // 监听打包失败事件
      const handlePackingFailed = (event: any, taskId: string, error: any) => {
        if (taskId === packingTaskId) {
          toast.error(`文件打包失败: ${error}`);
          reject(new Error(`打包失败: ${error}`));

          // 清理事件监听器
          window.removeEventListener("packing-task-completed", handlePackingComplete);
          window.removeEventListener("packing-task-failed", handlePackingFailed);
        }
      };

      // 添加事件监听器
      window.addEventListener("packing-task-completed", handlePackingComplete);
      window.addEventListener("packing-task-failed", handlePackingFailed);

      // 设置超时
      setTimeout(() => {
        window.removeEventListener("packing-task-completed", handlePackingComplete);
        window.removeEventListener("packing-task-failed", handlePackingFailed);
        reject(new Error("打包任务超时"));
      }, 10 * 60 * 1000); // 10分钟超时
    });
  };

  /**
   * 使用文件选择对话框上传
   */
  const uploadFromDialog = async (): Promise<void> => {
    try {
      isUploading.value = true;
      const api = getTusAPI();

      const result = await api.createUploadFromDialog();
      if (result.success && result.data?.taskIds) {
        const taskIds = result.data.taskIds;

        // 启动所有任务
        for (const taskId of taskIds) {
          await api.startUpload(taskId);
        }

        toast.success(`已添加 ${taskIds.length} 个文件到上传队列`);
      } else if (result.error && !result.error.includes("用户取消")) {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error("文件选择上传失败:", error);
      toast.error(`文件选择失败: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      isUploading.value = false;
    }
  };

  /**
   * 获取所有上传任务
   */
  const getAllTasks = async () => {
    try {
      const api = getTusAPI();
      const result = await api.getAllTasks();
      if (result.success) {
        return result.data || [];
      }
      return [];
    } catch (error) {
      console.error("获取上传任务失败:", error);
      return [];
    }
  };

  /**
   * 获取所有打包任务
   */
  const getAllPackingTasks = async () => {
    try {
      const api = getTusAPI();
      const result = await api.getAllPackingTasks();
      if (result.success) {
        return result.data || [];
      }
      return [];
    } catch (error) {
      console.error("获取打包任务失败:", error);
      return [];
    }
  };

  /**
   * 暂停上传任务
   */
  const pauseUpload = async (taskId: string): Promise<void> => {
    try {
      const api = getTusAPI();
      const result = await api.pauseUpload(taskId);
      if (!result.success) {
        throw new Error(result.error || "暂停上传失败");
      }
    } catch (error) {
      console.error("暂停上传失败:", error);
      toast.error(`暂停上传失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  };

  /**
   * 恢复上传任务
   */
  const resumeUpload = async (taskId: string): Promise<void> => {
    try {
      const api = getTusAPI();
      const result = await api.resumeUpload(taskId);
      if (!result.success) {
        throw new Error(result.error || "恢复上传失败");
      }
    } catch (error) {
      console.error("恢复上传失败:", error);
      toast.error(`恢复上传失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  };

  /**
   * 取消上传任务
   */
  const cancelUpload = async (taskId: string): Promise<void> => {
    try {
      const api = getTusAPI();
      const result = await api.cancelUpload(taskId);
      if (!result.success) {
        throw new Error(result.error || "取消上传失败");
      }
    } catch (error) {
      console.error("取消上传失败:", error);
      toast.error(`取消上传失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  };

  /**
   * 重试上传任务
   */
  const retryUpload = async (taskId: string): Promise<void> => {
    try {
      const api = getTusAPI();
      const result = await api.retryUpload(taskId);
      if (!result.success) {
        throw new Error(result.error || "重试上传失败");
      }
    } catch (error) {
      console.error("重试上传失败:", error);
      toast.error(`重试上传失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  };

  /**
   * 清理已完成的任务
   */
  const clearCompletedTasks = async (): Promise<void> => {
    try {
      const api = getTusAPI();
      const result = await api.clearCompletedTasks();
      if (!result.success) {
        throw new Error(result.error || "清理任务失败");
      }
      toast.success("已清理完成的任务");
    } catch (error) {
      console.error("清理任务失败:", error);
      toast.error(`清理任务失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  };

  // 任务管理计算属性
  const allTasks = computed(() => tasks.value);
  const allBatchTasks = computed(() => batchTasks.value);

  const standaloneTasks = computed(() => {
    return Array.from(tasks.value.values()).filter((task) => !task.isSubTask);
  });

  const activeTasks = computed(() => {
    return Array.from(tasks.value.values()).filter((task) => ["pending", "uploading"].includes(task.status));
  });

  const uploadingTasks = computed(() => {
    return Array.from(tasks.value.values()).filter((task) => task.status === "uploading");
  });

  const pausedTasks = computed(() => {
    return Array.from(tasks.value.values()).filter((task) => task.status === "paused");
  });

  const completedTasks = computed(() => {
    return Array.from(tasks.value.values()).filter((task) => task.status === "completed");
  });

  const errorTasks = computed(() => {
    return Array.from(tasks.value.values()).filter((task) => task.status === "error");
  });

  const totalProgress = computed(() => {
    const activeTasksArray = activeTasks.value;
    if (activeTasksArray.length === 0) return 0;

    const totalProgress = activeTasksArray.reduce((sum, task) => sum + task.progress, 0);
    return Math.round(totalProgress / activeTasksArray.length);
  });

  // 刷新任务列表
  const refreshTasks = async () => {
    if (!isElectron.value) return;

    try {
      console.log("[TUS] 开始刷新任务列表...");
      const response: ApiResponse = await getTusAPI().getAllTasks();
      if (response.success && response.tasks) {
        tusLogger.task(`获取到 ${response.tasks.length} 个TUS任务`);
        console.log(`[TUS] 从主进程获取到 ${response.tasks.length} 个任务`);

        const oldTaskCount = tasks.value.size;
        tasks.value.clear();
        response.tasks.forEach((task) => {
          tasks.value.set(task.id, task);
        });

        console.log(`[TUS] 任务列表已更新: ${oldTaskCount} -> ${tasks.value.size}`);
        tusLogger.task(`已同步 ${response.tasks.length} 个任务到前端`);
      } else {
        tusLogger.warn("获取TUS任务失败", response);
        console.warn("[TUS] 获取任务列表失败:", response);
      }
    } catch (error) {
      tusLogger.error("同步TUS任务时发生错误", error);
      console.error("[TUS] 刷新任务列表时出错:", error);
    }
  };

  // 批量任务操作方法
  const deleteBatchTask = async (batchId: string): Promise<void> => {
    try {
      // 删除批量任务中的所有子任务
      const batchTaskIds = Array.from(tasks.value.values())
        .filter((task) => task.batchId === batchId)
        .map((task) => task.id);

      for (const taskId of batchTaskIds) {
        await cancelUpload(taskId);
        tasks.value.delete(taskId);
      }

      // 删除批量任务记录
      batchTasks.value.delete(batchId);
      tusLogger.batch(`批量任务已删除: ${batchId}`);
    } catch (error) {
      tusLogger.error(`删除批量任务失败: ${batchId}`, error);
      throw error;
    }
  };

  const retryBatchUpload = async (batchId: string): Promise<void> => {
    try {
      const batchTaskIds = Array.from(tasks.value.values())
        .filter((task) => task.batchId === batchId && task.status === "error")
        .map((task) => task.id);

      for (const taskId of batchTaskIds) {
        await retryUpload(taskId);
      }

      tusLogger.batch(`批量任务重试: ${batchId}, 重试任务数: ${batchTaskIds.length}`);
    } catch (error) {
      tusLogger.error(`重试批量任务失败: ${batchId}`, error);
      throw error;
    }
  };

  const pauseBatchUpload = async (batchId: string): Promise<void> => {
    try {
      const batchTaskIds = Array.from(tasks.value.values())
        .filter((task) => task.batchId === batchId && task.status === "uploading")
        .map((task) => task.id);

      for (const taskId of batchTaskIds) {
        await pauseUpload(taskId);
      }

      tusLogger.batch(`批量任务暂停: ${batchId}, 暂停任务数: ${batchTaskIds.length}`);
    } catch (error) {
      tusLogger.error(`暂停批量任务失败: ${batchId}`, error);
      throw error;
    }
  };

  const resumeBatchUpload = async (batchId: string): Promise<void> => {
    try {
      const batchTaskIds = Array.from(tasks.value.values())
        .filter((task) => task.batchId === batchId && task.status === "paused")
        .map((task) => task.id);

      for (const taskId of batchTaskIds) {
        await resumeUpload(taskId);
      }

      tusLogger.batch(`批量任务恢复: ${batchId}, 恢复任务数: ${batchTaskIds.length}`);
    } catch (error) {
      tusLogger.error(`恢复批量任务失败: ${batchId}`, error);
      throw error;
    }
  };

  // 调试方法
  const debugTasksInfo = () => {
    console.log(`[TUS调试] 当前任务总数: ${tasks.value.size}`);
    console.log(`[TUS调试] 独立任务数: ${standaloneTasks.value.length}`);
    console.log(`[TUS调试] 批量任务数: ${batchTasks.value.size}`);
    console.log(`[TUS调试] 事件监听器状态: ${isListenersSetup.value ? "已设置" : "未设置"}`);

    tasks.value.forEach((task, taskId) => {
      console.log(`[TUS调试] 任务 ${taskId}: ${task.fileName} - ${task.status} (${task.progress}%)`);
    });
  };

  // 计算属性
  const hasActiveTasks = computed(() => {
    return activeTasks.value.length > 0 || Object.keys(packingTasks.value).length > 0;
  });

  // 生命周期管理
  onMounted(async () => {
    try {
      await setupEventListenersWithRetry();
      await refreshTasks();
      tusLogger.event("TUS 模块初始化完成");
    } catch (error) {
      tusLogger.error("TUS 模块初始化失败:", error);
    }
  });

  onUnmounted(() => {
    try {
      cleanupEventListeners();
    } catch (error) {
      tusLogger.error("TUS 模块清理失败:", error);
    }
  });

  return {
    // 基础状态
    isElectron,
    isUploading,
    uploadProgress,
    uploadTasks,
    packingTasks,
    currentStrategy,
    hasActiveTasks,

    // 任务状态
    tasks: allTasks,
    batchTasks: allBatchTasks,
    standaloneTasks,
    activeTasks,
    uploadingTasks,
    pausedTasks,
    completedTasks,
    errorTasks,
    totalProgress,

    // 主要方法
    smartUploadFiles,
    uploadFromDialog,

    // 任务操作
    pauseUpload,
    resumeUpload,
    cancelUpload,
    retryUpload,
    clearCompletedTasks,

    // 批量任务操作
    deleteBatchTask,
    retryBatchUpload,
    pauseBatchUpload,
    resumeBatchUpload,

    // 任务查询
    getAllTasks,
    getAllPackingTasks,
    refreshTasks,

    // 调试方法
    debugTasksInfo,

    // 工具函数
    formatFileSize,
  };
}
