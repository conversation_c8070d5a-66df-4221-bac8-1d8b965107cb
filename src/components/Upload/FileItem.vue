<template>
  <div
    class="relative overflow-hidden transition-all duration-200 border rounded-lg group border-border bg-card hover:bg-accent hover:shadow-md"
    :class="{
      'border-primary bg-primary/5': file.status === 'uploading',
      'border-green-500 bg-green-50 dark:bg-green-950/20': file.status === 'success',
      'border-destructive bg-destructive/5': file.status === 'error',
      'flex flex-col w-full max-w-[120px]': viewMode === 'grid',
      'w-full max-w-none': viewMode === 'list'
    }">
    <!-- 网格视图布局 -->
    <template v-if="viewMode === 'grid'">
      <!-- 文件预览 -->
      <div
        class="w-full h-[70px] pt-2 px-2 overflow-hidden rounded-t-lg bg-gradient-to-b from-background/50 to-muted/30">
        <FilePreview :file="file.file" :file-name="file.name" :file-type="file.type" container-class="w-full h-full"
          icon-class="w-5 h-5" />
      </div>

      <!-- 文件信息 -->
      <div class="flex flex-col flex-1 min-h-0 gap-1 p-2">
        <div class="text-xs font-medium leading-4 truncate text-foreground" :title="file.name">{{ file.name }}</div>
        <div class="flex flex-col gap-0.5 text-[10px]">
          <span class="text-muted-foreground">{{ formatFileSize(file.size) }}</span>
          <span v-if="file.status === 'error' && file.error" class="font-medium text-destructive">错误</span>
          <span v-else-if="file.status === 'success'" class="font-medium text-green-600">成功</span>
          <span v-else-if="file.status === 'uploading'" class="font-medium text-primary">
            {{ file.progress ? `${file.progress}%` : '上传中' }}
          </span>
        </div>

        <!-- 进度条 -->
        <div v-if="file.status === 'uploading'" class="w-full h-0.5 bg-muted rounded-sm overflow-hidden">
          <div class="h-full transition-all duration-300 rounded-sm bg-primary"
            :style="{ width: `${file.progress || 0}%` }"></div>
        </div>
      </div>

      <!-- 移除按钮 -->
      <div class="absolute transition-opacity duration-200 opacity-0 top-1 right-1 group-hover:opacity-100">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger as-child>
              <Button variant="ghost" size="sm" class="w-6 h-6 p-0" @click="handleRemove">
                <X class="w-3 h-3" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>移除文件</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
    </template>

    <!-- 列表视图布局 -->
    <template v-else>
      <div class="flex items-center p-3 sm:p-4 gap-4 min-h-[60px]">
        <!-- 文件预览/图标 -->
        <div class="flex-shrink-0 w-8 h-8 overflow-hidden rounded-md sm:w-10 sm:h-10">
          <FilePreview :file="file.file" :file-name="file.name" :file-type="file.type" container-class="w-full h-full"
            icon-class="w-4 h-4 sm:w-5 sm:h-5" />
        </div>

        <!-- 文件基本信息 -->
        <div class="flex flex-col flex-1 min-w-0 gap-2">
          <div class="flex items-center justify-between gap-4">
            <span class="text-sm font-medium truncate text-foreground" :title="file.name">{{ file.name }}</span>
            <span class="text-sm text-muted-foreground shrink-0">{{ formatFileSize(file.size) }}</span>
          </div>

          <!-- 状态和进度 -->
          <div class="flex flex-col gap-1">
            <div class="text-xs">
              <span v-if="file.status === 'error' && file.error" class="font-medium text-destructive">{{ file.error
                }}</span>
              <span v-else-if="file.status === 'success'" class="font-medium text-green-600">上传成功</span>
              <span v-else-if="file.status === 'uploading'" class="font-medium text-primary">
                上传中 {{ file.progress ? `${file.progress}%` : '' }}
              </span>
              <span v-else class="text-muted-foreground">等待上传</span>
            </div>

            <!-- 进度条 -->
            <div v-if="file.status === 'uploading'"
              class="w-full max-w-[200px] h-0.5 bg-muted rounded-full overflow-hidden">
              <div class="h-full transition-all duration-300 rounded-full bg-primary"
                :style="{ width: `${file.progress || 0}%` }"></div>
            </div>
          </div>
        </div>

        <!-- 移除按钮 -->
        <div class="flex items-center shrink-0">
          <Button variant="ghost" size="sm" @click="handleRemove">
            <X class="w-4 h-4" />
          </Button>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { X } from 'lucide-vue-next'
import { Button } from '@/components/ui/button'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import FilePreview from './FilePreview.vue'
import { formatFileSize } from '@/lib/upload-utils'
// import type { ElectronUploadFile } from './composables/useElectronFileUpload' // 暂时未使用

// 兼容性类型定义
interface UploadFile {
  id: string
  name: string
  size: number
  type: string
  file?: any // 兼容性字段
  status?: string
  progress?: number
  error?: string
}

// Props
const props = withDefaults(defineProps<{
  file: UploadFile
  viewMode?: 'grid' | 'list'
}>(), {
  viewMode: 'grid'
})

// Emits
const emit = defineEmits<{
  remove: [fileId: string]
}>()

// 处理移除
const handleRemove = () => {
  emit('remove', props.file.id)
}
</script>
